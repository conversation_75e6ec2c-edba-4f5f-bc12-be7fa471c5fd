import { Sprite } from "laya/display/Sprite";
import { Text } from "laya/display/Text";
import { Graphics } from "laya/display/Graphics";

export enum Suit {
    HEARTS = "♥",
    DIAMONDS = "♦", 
    CLUBS = "♣",
    SPADES = "♠"
}

export enum Rank {
    ACE = "A",
    TWO = "2",
    THREE = "3", 
    FOUR = "4",
    FIVE = "5",
    SIX = "6",
    SEVEN = "7",
    EIGHT = "8",
    NINE = "9",
    TEN = "10",
    JACK = "J",
    QUEEN = "Q",
    KING = "K"
}

export class Card extends Sprite {
    public suit: Suit;
    public rank: Rank;
    public isHidden: boolean = false;
    
    private cardWidth: number = 80;
    private cardHeight: number = 120;
    private rankText: Text;
    private suitText: Text;
    private backSprite: Sprite;

    constructor(suit: Suit, rank: Rank) {
        super();
        this.suit = suit;
        this.rank = rank;
        this.createCard();
    }

    private createCard(): void {
        this.graphics.drawRect(0, 0, this.cardWidth, this.cardHeight, "#ffffff", "#000000", 2);
        
        this.rankText = new Text();
        this.rankText.text = this.rank;
        this.rankText.fontSize = 16;
        this.rankText.color = this.getColor();
        this.rankText.x = 5;
        this.rankText.y = 5;
        this.addChild(this.rankText);

        this.suitText = new Text();
        this.suitText.text = this.suit;
        this.suitText.fontSize = 24;
        this.suitText.color = this.getColor();
        this.suitText.x = this.cardWidth / 2 - 12;
        this.suitText.y = this.cardHeight / 2 - 12;
        this.addChild(this.suitText);

        this.createBackSprite();
    }

    private createBackSprite(): void {
        this.backSprite = new Sprite();
        this.backSprite.graphics.drawRect(0, 0, this.cardWidth, this.cardHeight, "#0066cc", "#000000", 2);
        
        const backText = new Text();
        backText.text = "CARD";
        backText.fontSize = 12;
        backText.color = "#ffffff";
        backText.x = this.cardWidth / 2 - 20;
        backText.y = this.cardHeight / 2 - 6;
        this.backSprite.addChild(backText);
        
        this.backSprite.visible = false;
        this.addChild(this.backSprite);
    }

    private getColor(): string {
        return (this.suit === Suit.HEARTS || this.suit === Suit.DIAMONDS) ? "#ff0000" : "#000000";
    }

    public getValue(): number {
        switch (this.rank) {
            case Rank.ACE:
                return 11;
            case Rank.JACK:
            case Rank.QUEEN:
            case Rank.KING:
                return 10;
            default:
                return parseInt(this.rank);
        }
    }

    public hide(): void {
        this.isHidden = true;
        this.rankText.visible = false;
        this.suitText.visible = false;
        this.backSprite.visible = true;
    }

    public show(): void {
        this.isHidden = false;
        this.rankText.visible = true;
        this.suitText.visible = true;
        this.backSprite.visible = false;
    }
}
