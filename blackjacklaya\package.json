{"name": "blackjack-lay<PERSON><PERSON>", "version": "1.0.0", "description": "BlackJack game built with LayaAir 3.0", "main": "index.js", "scripts": {"build": "node build.js", "dev": "node server.js", "start": "node server.js", "serve": "node server.js"}, "dependencies": {"layaair": "^3.0.0"}, "devDependencies": {"@types/node": "^18.0.0", "typescript": "^4.9.0"}, "keywords": ["<PERSON><PERSON><PERSON>", "blackjack", "game", "typescript"], "author": "", "license": "MIT"}