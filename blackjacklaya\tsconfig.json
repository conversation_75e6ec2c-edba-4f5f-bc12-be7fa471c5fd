{"compilerOptions": {"target": "ES2020", "module": "ESNext", "moduleResolution": "node", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": false, "outDir": "./bin/js", "rootDir": "./src", "experimentalDecorators": true, "emitDecoratorMetadata": true, "lib": ["ES2020", "DOM"], "types": ["node"]}, "include": ["src/**/*"], "exclude": ["node_modules", "bin"]}