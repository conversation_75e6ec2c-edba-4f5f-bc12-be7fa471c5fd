const fs = require('fs');
const path = require('path');

function compileTypeScript() {
    const srcDir = './src';
    const outDir = './bin/js';
    
    if (!fs.existsSync(outDir)) {
        fs.mkdirSync(outDir, { recursive: true });
    }
    
    const files = getAllTsFiles(srcDir);
    let compiledCode = '';
    
    for (const file of files) {
        const content = fs.readFileSync(file, 'utf8');
        const jsContent = transpileTypeScript(content);
        compiledCode += jsContent + '\n';
    }
    
    fs.writeFileSync(path.join(outDir, 'bundle.js'), compiledCode);
    console.log('TypeScript compilation completed!');
}

function getAllTsFiles(dir) {
    const files = [];
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
            files.push(...getAllTsFiles(fullPath));
        } else if (item.endsWith('.ts')) {
            files.push(fullPath);
        }
    }
    
    return files;
}

function transpileTypeScript(content) {
    return content
        .replace(/import\s+.*?from\s+['"].*?['"];?\s*/g, '')
        .replace(/export\s+/g, '')
        .replace(/public\s+/g, '')
        .replace(/private\s+/g, '')
        .replace(/protected\s+/g, '')
        .replace(/readonly\s+/g, '')
        .replace(/:\s*\w+(\[\])?(\s*\|\s*\w+)*\s*=/g, ' =')
        .replace(/:\s*\w+(\[\])?(\s*\|\s*\w+)*\s*;/g, ';')
        .replace(/:\s*\w+(\[\])?(\s*\|\s*\w+)*\s*\)/g, ')')
        .replace(/:\s*\w+(\[\])?(\s*\|\s*\w+)*\s*\{/g, ' {')
        .replace(/:\s*void\s*\{/g, ' {')
        .replace(/constructor\(/g, 'constructor(')
        .replace(/enum\s+(\w+)\s*\{([^}]+)\}/g, (match, name, body) => {
            const entries = body.split(',').map(entry => {
                const [key, value] = entry.split('=').map(s => s.trim());
                return `${key}: ${value}`;
            });
            return `const ${name} = { ${entries.join(', ')} };`;
        });
}

compileTypeScript();
