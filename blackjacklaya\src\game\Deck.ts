import { Card, Suit, Rank } from "./Card";

export class Deck {
    private cards: Card[] = [];

    constructor() {
        this.createDeck();
        this.shuffle();
    }

    private createDeck(): void {
        const suits = [Suit.HEARTS, Suit.DIAMONDS, Suit.CLUBS, Suit.SPADES];
        const ranks = [
            Rank.ACE, Rank.TWO, Rank.THREE, Rank.FOUR, Rank.FIVE, Rank.SIX,
            Rank.SEVEN, Rank.EIGHT, Rank.NINE, Rank.TEN, Rank.JACK, Rank.QUEEN, Rank.KING
        ];

        for (const suit of suits) {
            for (const rank of ranks) {
                this.cards.push(new Card(suit, rank));
            }
        }
    }

    private shuffle(): void {
        for (let i = this.cards.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [this.cards[i], this.cards[j]] = [this.cards[j], this.cards[i]];
        }
    }

    public dealCard(): Card | null {
        return this.cards.pop() || null;
    }

    public getCardsRemaining(): number {
        return this.cards.length;
    }

    public reset(): void {
        this.cards = [];
        this.createDeck();
        this.shuffle();
    }
}
