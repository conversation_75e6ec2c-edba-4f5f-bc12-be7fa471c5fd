import { Sprite } from "laya/display/Sprite";
import { Text } from "laya/display/Text";
import { Button } from "laya/ui/Button";
import { Deck } from "./Deck";
import { Hand } from "./Hand";
import { Card } from "./Card";

export enum GameState {
    WAITING,
    DEALING,
    PLAYER_TURN,
    DEALER_TURN,
    GAME_OVER
}

export class BlackjackGame extends Sprite {
    private deck: Deck;
    private playerHand: Hand;
    private dealerHand: Hand;
    private gameState: GameState = GameState.WAITING;
    
    private playerScore: number = 0;
    private dealerScore: number = 0;
    
    private dealButton: Button;
    private hitButton: Button;
    private standButton: Button;
    private newGameButton: Button;
    
    private playerValueText: Text;
    private dealerValueText: Text;
    private gameStatusText: Text;
    private scoreText: Text;

    constructor() {
        super();
        this.init();
    }

    private init(): void {
        this.deck = new Deck();
        this.createUI();
        this.createHands();
        this.updateUI();
    }

    private createUI(): void {
        const bg = new Sprite();
        bg.graphics.drawRect(0, 0, 1334, 750, "#0f4c3a");
        this.addChild(bg);

        this.gameStatusText = new Text();
        this.gameStatusText.text = "Welcome to Professional BlackJack Casino";
        this.gameStatusText.fontSize = 32;
        this.gameStatusText.color = "#ffffff";
        this.gameStatusText.x = 667 - 250;
        this.gameStatusText.y = 50;
        this.addChild(this.gameStatusText);

        this.scoreText = new Text();
        this.scoreText.text = `Player: ${this.playerScore} | Dealer: ${this.dealerScore}`;
        this.scoreText.fontSize = 24;
        this.scoreText.color = "#ffff00";
        this.scoreText.x = 667 - 150;
        this.scoreText.y = 100;
        this.addChild(this.scoreText);

        this.dealerValueText = new Text();
        this.dealerValueText.text = "Dealer: 0";
        this.dealerValueText.fontSize = 24;
        this.dealerValueText.color = "#ffffff";
        this.dealerValueText.x = 50;
        this.dealerValueText.y = 200;
        this.addChild(this.dealerValueText);

        this.playerValueText = new Text();
        this.playerValueText.text = "Player: 0";
        this.playerValueText.fontSize = 24;
        this.playerValueText.color = "#ffffff";
        this.playerValueText.x = 50;
        this.playerValueText.y = 500;
        this.addChild(this.playerValueText);

        this.createButtons();
    }

    private createButtons(): void {
        this.dealButton = this.createButton("Deal Cards", 667 - 60, 650);
        this.dealButton.on("click", this, this.dealCards);

        this.hitButton = this.createButton("Hit", 500, 650);
        this.hitButton.on("click", this, this.hit);
        this.hitButton.visible = false;

        this.standButton = this.createButton("Stand", 650, 650);
        this.standButton.on("click", this, this.stand);
        this.standButton.visible = false;

        this.newGameButton = this.createButton("New Game", 800, 650);
        this.newGameButton.on("click", this, this.newGame);
        this.newGameButton.visible = false;
    }

    private createButton(text: string, x: number, y: number): Button {
        const button = new Button();
        button.label = text;
        button.width = 120;
        button.height = 40;
        button.x = x;
        button.y = y;
        button.skin = this.createButtonSkin();
        this.addChild(button);
        return button;
    }

    private createButtonSkin(): string {
        const canvas = document.createElement('canvas');
        canvas.width = 120;
        canvas.height = 40;
        const ctx = canvas.getContext('2d')!;
        
        ctx.fillStyle = '#4CAF50';
        ctx.fillRect(0, 0, 120, 40);
        ctx.strokeStyle = '#45a049';
        ctx.lineWidth = 2;
        ctx.strokeRect(0, 0, 120, 40);
        
        return canvas.toDataURL();
    }

    private createHands(): void {
        this.dealerHand = new Hand();
        this.dealerHand.x = 100;
        this.dealerHand.y = 250;
        this.addChild(this.dealerHand);

        this.playerHand = new Hand();
        this.playerHand.x = 100;
        this.playerHand.y = 400;
        this.addChild(this.playerHand);
    }

    private dealCards(): void {
        if (this.gameState !== GameState.WAITING) return;

        this.gameState = GameState.DEALING;
        this.clearHands();

        if (this.deck.getCardsRemaining() < 10) {
            this.deck.reset();
        }

        this.playerHand.addCard(this.deck.dealCard()!);
        this.dealerHand.addCard(this.deck.dealCard()!);
        this.playerHand.addCard(this.deck.dealCard()!);

        const dealerCard = this.deck.dealCard()!;
        dealerCard.hide();
        this.dealerHand.addCard(dealerCard);

        this.gameState = GameState.PLAYER_TURN;
        this.updateUI();
        this.checkForBlackjack();
    }

    private hit(): void {
        if (this.gameState !== GameState.PLAYER_TURN) return;

        this.playerHand.addCard(this.deck.dealCard()!);
        this.updateUI();

        if (this.playerHand.isBusted()) {
            this.endGame("Player Busted! Dealer Wins!");
            this.dealerScore++;
        }
    }

    private stand(): void {
        if (this.gameState !== GameState.PLAYER_TURN) return;

        this.gameState = GameState.DEALER_TURN;
        this.dealerHand.showAllCards();
        this.dealerPlay();
    }

    private dealerPlay(): void {
        const dealerValue = this.dealerHand.getValue();

        if (dealerValue < 17) {
            setTimeout(() => {
                this.dealerHand.addCard(this.deck.dealCard()!);
                this.updateUI();
                this.dealerPlay();
            }, 1000);
        } else {
            this.determineWinner();
        }
    }

    private checkForBlackjack(): void {
        if (this.playerHand.isBlackjack()) {
            this.dealerHand.showAllCards();
            if (this.dealerHand.isBlackjack()) {
                this.endGame("Both have BlackJack! It's a Push!");
            } else {
                this.endGame("BlackJack! Player Wins!");
                this.playerScore++;
            }
        }
    }

    private determineWinner(): void {
        const playerValue = this.playerHand.getValue();
        const dealerValue = this.dealerHand.getValue();

        if (dealerValue > 21) {
            this.endGame("Dealer Busted! Player Wins!");
            this.playerScore++;
        } else if (playerValue > dealerValue) {
            this.endGame("Player Wins!");
            this.playerScore++;
        } else if (dealerValue > playerValue) {
            this.endGame("Dealer Wins!");
            this.dealerScore++;
        } else {
            this.endGame("It's a Push!");
        }
    }

    private endGame(message: string): void {
        this.gameState = GameState.GAME_OVER;
        this.gameStatusText.text = message;
        this.updateUI();
    }

    private newGame(): void {
        this.gameState = GameState.WAITING;
        this.clearHands();
        this.updateUI();
    }

    private clearHands(): void {
        this.playerHand.clear();
        this.dealerHand.clear();
    }

    private updateUI(): void {
        const playerValue = this.playerHand.getValue();
        const dealerValue = this.gameState === GameState.PLAYER_TURN ?
            this.dealerHand.getCards()[1]?.getValue() || 0 :
            this.dealerHand.getValue();

        this.playerValueText.text = `Player: ${playerValue}`;
        this.dealerValueText.text = `Dealer: ${dealerValue}`;
        this.scoreText.text = `Player: ${this.playerScore} | Dealer: ${this.dealerScore}`;

        this.dealButton.visible = this.gameState === GameState.WAITING;
        this.hitButton.visible = this.gameState === GameState.PLAYER_TURN;
        this.standButton.visible = this.gameState === GameState.PLAYER_TURN;
        this.newGameButton.visible = this.gameState === GameState.GAME_OVER;

        if (this.gameState === GameState.WAITING) {
            this.gameStatusText.text = "Welcome to Professional BlackJack Casino";
        }
    }
}
