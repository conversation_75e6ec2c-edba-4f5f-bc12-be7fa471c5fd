const Suit = {
    HEARTS: "♥",
    DIAMONDS: "♦", 
    CLUBS: "♣",
    SPADES: "♠"
};

const Rank = {
    ACE: "A",
    TWO: "2",
    THREE: "3", 
    FOUR: "4",
    FIVE: "5",
    SIX: "6",
    SEVEN: "7",
    EIGHT: "8",
    NINE: "9",
    TEN: "10",
    JACK: "J",
    QUEEN: "Q",
    KING: "K"
};

const GameState = {
    WAITING: 0,
    DEALING: 1,
    PLAYER_TURN: 2,
    DEALER_TURN: 3,
    GAME_OVER: 4
};

class Card extends Sprite {
    constructor(suit, rank) {
        super();
        this.suit = suit;
        this.rank = rank;
        this.isHidden = false;
        this.cardWidth = 80;
        this.cardHeight = 120;
        this.createCard();
    }

    createCard() {
        this.graphics.drawRect(0, 0, this.cardWidth, this.cardHeight, "#ffffff", "#000000", 2);
        
        this.rankText = new Text();
        this.rankText.text = this.rank;
        this.rankText.fontSize = 16;
        this.rankText.color = this.getColor();
        this.rankText.x = 5;
        this.rankText.y = 5;
        this.addChild(this.rankText);

        this.suitText = new Text();
        this.suitText.text = this.suit;
        this.suitText.fontSize = 24;
        this.suitText.color = this.getColor();
        this.suitText.x = this.cardWidth / 2 - 12;
        this.suitText.y = this.cardHeight / 2 - 12;
        this.addChild(this.suitText);

        this.createBackSprite();
    }

    createBackSprite() {
        this.backSprite = new Sprite();
        this.backSprite.graphics.drawRect(0, 0, this.cardWidth, this.cardHeight, "#0066cc", "#000000", 2);
        
        const backText = new Text();
        backText.text = "CARD";
        backText.fontSize = 12;
        backText.color = "#ffffff";
        backText.x = this.cardWidth / 2 - 20;
        backText.y = this.cardHeight / 2 - 6;
        this.backSprite.addChild(backText);
        
        this.backSprite.visible = false;
        this.addChild(this.backSprite);
    }

    getColor() {
        return (this.suit === Suit.HEARTS || this.suit === Suit.DIAMONDS) ? "#ff0000" : "#000000";
    }

    getValue() {
        switch (this.rank) {
            case Rank.ACE:
                return 11;
            case Rank.JACK:
            case Rank.QUEEN:
            case Rank.KING:
                return 10;
            default:
                return parseInt(this.rank);
        }
    }

    hide() {
        this.isHidden = true;
        this.rankText.visible = false;
        this.suitText.visible = false;
        this.backSprite.visible = true;
    }

    show() {
        this.isHidden = false;
        this.rankText.visible = true;
        this.suitText.visible = true;
        this.backSprite.visible = false;
    }
}

class Deck {
    constructor() {
        this.cards = [];
        this.createDeck();
        this.shuffle();
    }

    createDeck() {
        const suits = [Suit.HEARTS, Suit.DIAMONDS, Suit.CLUBS, Suit.SPADES];
        const ranks = [
            Rank.ACE, Rank.TWO, Rank.THREE, Rank.FOUR, Rank.FIVE, Rank.SIX,
            Rank.SEVEN, Rank.EIGHT, Rank.NINE, Rank.TEN, Rank.JACK, Rank.QUEEN, Rank.KING
        ];

        for (const suit of suits) {
            for (const rank of ranks) {
                this.cards.push(new Card(suit, rank));
            }
        }
    }

    shuffle() {
        for (let i = this.cards.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [this.cards[i], this.cards[j]] = [this.cards[j], this.cards[i]];
        }
    }

    dealCard() {
        return this.cards.pop() || null;
    }

    getCardsRemaining() {
        return this.cards.length;
    }

    reset() {
        this.cards = [];
        this.createDeck();
        this.shuffle();
    }
}

class Hand extends Sprite {
    constructor() {
        super();
        this.cards = [];
        this.cardSpacing = 90;
    }

    addCard(card, animated = true) {
        this.cards.push(card);
        this.addChild(card);
        
        const targetX = this.cards.length * this.cardSpacing - this.cardSpacing;
        const targetY = 0;

        if (animated) {
            card.x = -100;
            card.y = -50;
            Tween.to(card, { x: targetX, y: targetY }, 500, Ease.backOut);
        } else {
            card.x = targetX;
            card.y = targetY;
        }
    }

    getValue() {
        let value = 0;
        let aces = 0;

        for (const card of this.cards) {
            if (card.rank === Rank.ACE) {
                aces++;
                value += 11;
            } else {
                value += card.getValue();
            }
        }

        while (value > 21 && aces > 0) {
            value -= 10;
            aces--;
        }

        return value;
    }

    isBusted() {
        return this.getValue() > 21;
    }

    isBlackjack() {
        return this.cards.length === 2 && this.getValue() === 21;
    }

    getCardCount() {
        return this.cards.length;
    }

    getCards() {
        return this.cards;
    }

    clear() {
        for (const card of this.cards) {
            this.removeChild(card);
        }
        this.cards = [];
    }

    showAllCards() {
        for (const card of this.cards) {
            card.show();
        }
    }

    hideFirstCard() {
        if (this.cards.length > 0) {
            this.cards[0].hide();
        }
    }
}

class BlackjackGame extends Sprite {
    constructor() {
        super();
        this.deck = new Deck();
        this.gameState = GameState.WAITING;
        this.playerScore = 0;
        this.dealerScore = 0;
        this.init();
    }

    init() {
        this.createUI();
        this.createHands();
        this.updateUI();
    }

    createUI() {
        const bg = new Sprite();
        bg.graphics.drawRect(0, 0, 1334, 750, "#0f4c3a");
        this.addChild(bg);

        this.gameStatusText = new Text();
        this.gameStatusText.text = "Welcome to Professional BlackJack Casino";
        this.gameStatusText.fontSize = 32;
        this.gameStatusText.color = "#ffffff";
        this.gameStatusText.x = 667 - 250;
        this.gameStatusText.y = 50;
        this.addChild(this.gameStatusText);

        this.scoreText = new Text();
        this.scoreText.text = `Player: ${this.playerScore} | Dealer: ${this.dealerScore}`;
        this.scoreText.fontSize = 24;
        this.scoreText.color = "#ffff00";
        this.scoreText.x = 667 - 150;
        this.scoreText.y = 100;
        this.addChild(this.scoreText);

        this.dealerValueText = new Text();
        this.dealerValueText.text = "Dealer: 0";
        this.dealerValueText.fontSize = 24;
        this.dealerValueText.color = "#ffffff";
        this.dealerValueText.x = 50;
        this.dealerValueText.y = 200;
        this.addChild(this.dealerValueText);

        this.playerValueText = new Text();
        this.playerValueText.text = "Player: 0";
        this.playerValueText.fontSize = 24;
        this.playerValueText.color = "#ffffff";
        this.playerValueText.x = 50;
        this.playerValueText.y = 500;
        this.addChild(this.playerValueText);

        this.createButtons();
    }

    createButtons() {
        this.dealButton = this.createButton("Deal Cards", 667 - 60, 650);
        this.dealButton.on("click", this, this.dealCards);

        this.hitButton = this.createButton("Hit", 500, 650);
        this.hitButton.on("click", this, this.hit);
        this.hitButton.visible = false;

        this.standButton = this.createButton("Stand", 650, 650);
        this.standButton.on("click", this, this.stand);
        this.standButton.visible = false;

        this.newGameButton = this.createButton("New Game", 800, 650);
        this.newGameButton.on("click", this, this.newGame);
        this.newGameButton.visible = false;
    }

    createButton(text, x, y) {
        const button = new Button();
        button.label = text;
        button.width = 120;
        button.height = 40;
        button.x = x;
        button.y = y;
        this.addChild(button);
        return button;
    }

    createHands() {
        this.dealerHand = new Hand();
        this.dealerHand.x = 100;
        this.dealerHand.y = 250;
        this.addChild(this.dealerHand);

        this.playerHand = new Hand();
        this.playerHand.x = 100;
        this.playerHand.y = 400;
        this.addChild(this.playerHand);
    }

    dealCards() {
        if (this.gameState !== GameState.WAITING) return;

        this.gameState = GameState.DEALING;
        this.clearHands();

        if (this.deck.getCardsRemaining() < 10) {
            this.deck.reset();
        }

        this.playerHand.addCard(this.deck.dealCard());
        this.dealerHand.addCard(this.deck.dealCard());
        this.playerHand.addCard(this.deck.dealCard());

        const dealerCard = this.deck.dealCard();
        dealerCard.hide();
        this.dealerHand.addCard(dealerCard);

        this.gameState = GameState.PLAYER_TURN;
        this.updateUI();
        this.checkForBlackjack();
    }

    hit() {
        if (this.gameState !== GameState.PLAYER_TURN) return;

        this.playerHand.addCard(this.deck.dealCard());
        this.updateUI();

        if (this.playerHand.isBusted()) {
            this.endGame("Player Busted! Dealer Wins!");
            this.dealerScore++;
        }
    }

    stand() {
        if (this.gameState !== GameState.PLAYER_TURN) return;

        this.gameState = GameState.DEALER_TURN;
        this.dealerHand.showAllCards();
        this.dealerPlay();
    }

    dealerPlay() {
        const dealerValue = this.dealerHand.getValue();

        if (dealerValue < 17) {
            setTimeout(() => {
                this.dealerHand.addCard(this.deck.dealCard());
                this.updateUI();
                this.dealerPlay();
            }, 1000);
        } else {
            this.determineWinner();
        }
    }

    checkForBlackjack() {
        if (this.playerHand.isBlackjack()) {
            this.dealerHand.showAllCards();
            if (this.dealerHand.isBlackjack()) {
                this.endGame("Both have BlackJack! It's a Push!");
            } else {
                this.endGame("BlackJack! Player Wins!");
                this.playerScore++;
            }
        }
    }

    determineWinner() {
        const playerValue = this.playerHand.getValue();
        const dealerValue = this.dealerHand.getValue();

        if (dealerValue > 21) {
            this.endGame("Dealer Busted! Player Wins!");
            this.playerScore++;
        } else if (playerValue > dealerValue) {
            this.endGame("Player Wins!");
            this.playerScore++;
        } else if (dealerValue > playerValue) {
            this.endGame("Dealer Wins!");
            this.dealerScore++;
        } else {
            this.endGame("It's a Push!");
        }
    }

    endGame(message) {
        this.gameState = GameState.GAME_OVER;
        this.gameStatusText.text = message;
        this.updateUI();
    }

    newGame() {
        this.gameState = GameState.WAITING;
        this.clearHands();
        this.updateUI();
    }

    clearHands() {
        this.playerHand.clear();
        this.dealerHand.clear();
    }

    updateUI() {
        const playerValue = this.playerHand.getValue();
        const dealerValue = this.gameState === GameState.PLAYER_TURN ?
            (this.dealerHand.getCards()[1] ? this.dealerHand.getCards()[1].getValue() : 0) :
            this.dealerHand.getValue();

        this.playerValueText.text = `Player: ${playerValue}`;
        this.dealerValueText.text = `Dealer: ${dealerValue}`;
        this.scoreText.text = `Player: ${this.playerScore} | Dealer: ${this.dealerScore}`;

        this.dealButton.visible = this.gameState === GameState.WAITING;
        this.hitButton.visible = this.gameState === GameState.PLAYER_TURN;
        this.standButton.visible = this.gameState === GameState.PLAYER_TURN;
        this.newGameButton.visible = this.gameState === GameState.GAME_OVER;

        if (this.gameState === GameState.WAITING) {
            this.gameStatusText.text = "Welcome to Professional BlackJack Casino";
        }
    }
}

class Main {
    constructor() {
        Laya.init(1334, 750).then(() => {
            Laya.stage.scaleMode = 'fixedWidth';
            Laya.stage.screenMode = 'horizontal';
            Laya.stage.alignV = 'middle';
            Laya.stage.alignH = 'center';

            Stat.show();

            this.startGame();
        });
    }

    startGame() {
        const game = new BlackjackGame();
        Laya.stage.addChild(game);
    }
}

window.addEventListener('load', () => {
    new Main();
});
