import { Sprite } from "laya/display/Sprite";
import { Card, Rank } from "./Card";
import { Tween } from "laya/utils/Tween";
import { Ease } from "laya/utils/Ease";

export class Hand extends Sprite {
    private cards: Card[] = [];
    private cardSpacing: number = 90;

    constructor() {
        super();
    }

    public addCard(card: Card, animated: boolean = true): void {
        this.cards.push(card);
        this.addChild(card);
        
        const targetX = this.cards.length * this.cardSpacing - this.cardSpacing;
        const targetY = 0;

        if (animated) {
            card.x = -100;
            card.y = -50;
            Tween.to(card, { x: targetX, y: targetY }, 500, Ease.backOut);
        } else {
            card.x = targetX;
            card.y = targetY;
        }
    }

    public getValue(): number {
        let value = 0;
        let aces = 0;

        for (const card of this.cards) {
            if (card.rank === Rank.ACE) {
                aces++;
                value += 11;
            } else {
                value += card.getValue();
            }
        }

        while (value > 21 && aces > 0) {
            value -= 10;
            aces--;
        }

        return value;
    }

    public isBusted(): boolean {
        return this.getValue() > 21;
    }

    public isBlackjack(): boolean {
        return this.cards.length === 2 && this.getValue() === 21;
    }

    public getCardCount(): number {
        return this.cards.length;
    }

    public getCards(): Card[] {
        return this.cards;
    }

    public clear(): void {
        for (const card of this.cards) {
            this.removeChild(card);
        }
        this.cards = [];
    }

    public showAllCards(): void {
        for (const card of this.cards) {
            card.show();
        }
    }

    public hideFirstCard(): void {
        if (this.cards.length > 0) {
            this.cards[0].hide();
        }
    }
}
