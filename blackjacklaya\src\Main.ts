import { Laya } from "Laya";
import { Stage } from "laya/display/Stage";
import { Stat } from "laya/utils/Stat";
import { BlackjackGame } from "./game/BlackjackGame";

class Main {
    constructor() {
        Laya.init(1334, 750).then(() => {
            Laya.stage.scaleMode = Stage.SCALE_FIXED_WIDTH;
            Laya.stage.screenMode = Stage.SCREEN_HORIZONTAL;
            Laya.stage.alignV = Stage.ALIGN_MIDDLE;
            Laya.stage.alignH = Stage.ALIGN_CENTER;
            
            Stat.show();
            
            this.startGame();
        });
    }

    private startGame(): void {
        const game = new BlackjackGame();
        Laya.stage.addChild(game);
    }
}

new Main();
