# BlackJack Game - LayaAir 3.0

Professional casino-style BlackJack card game built with LayaAir 3.0 engine.

## Features

- **Professional Casino Experience**: Authentic BlackJack gameplay with realistic card mechanics
- **Responsive Design**: Optimized for desktop and mobile devices
- **Smooth Animations**: Card dealing and movement animations using LayaAir's Tween system
- **Score Tracking**: Keep track of player vs dealer wins
- **SEO Optimized**: Built with search engine optimization for online casino games

## Game Rules

- Goal: Get as close to 21 as possible without going over
- Face cards (J, Q, K) are worth 10 points
- Aces are worth 11 or 1 (automatically calculated for best hand)
- Dealer must hit on 16 and stand on 17
- BlackJack (21 with first 2 cards) beats regular 21

## How to Play

1. Click "Deal Cards" to start a new hand
2. You'll receive 2 cards face up, dealer gets 1 face up and 1 face down
3. Click "Hit" to take another card or "Stand" to keep your current total
4. Dealer reveals hidden card and plays according to casino rules
5. Winner is determined and score is updated

## Installation & Running

### Prerequisites
- Node.js (version 12 or higher)

### Setup
```bash
# Clone or download the project
cd blackjacklaya

# Install dependencies (optional - no external dependencies required)
npm install

# Start the development server
npm run dev
```

### Alternative Running Methods
```bash
# Using Node.js directly
node server.js

# Or simply open index.html in a web browser
```

## Project Structure

```
blackjacklaya/
├── src/                    # TypeScript source files
│   ├── Main.ts            # Application entry point
│   └── game/              # Game logic
│       ├── BlackjackGame.ts
│       ├── Card.ts
│       ├── Deck.ts
│       └── Hand.ts
├── libs/                  # LayaAir library files
│   └── laya.js           # Simplified LayaAir implementation
├── bin/js/               # Compiled JavaScript
│   └── bundle.js         # Game bundle
├── assets/               # Game assets
│   ├── images/
│   └── sounds/
├── index.html            # Main HTML file
├── package.json          # Project configuration
├── tsconfig.json         # TypeScript configuration
├── build.js              # Build script
└── server.js             # Development server
```

## Technologies Used

- **LayaAir 3.0**: Game engine for 2D/3D games
- **TypeScript**: Type-safe JavaScript development
- **HTML5 Canvas**: Rendering graphics and animations
- **Node.js**: Development server and build tools

## Browser Compatibility

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+
- Mobile browsers with HTML5 Canvas support

## Development

To modify the game:

1. Edit TypeScript files in the `src/` directory
2. Run `npm run build` to compile TypeScript to JavaScript
3. Refresh the browser to see changes

## SEO Keywords

BlackJack, card game, casino, online blackjack, 21 card game, casino games, card games online, LayaAir games, HTML5 games, browser games

## License

MIT License - Feel free to use and modify for your projects.
