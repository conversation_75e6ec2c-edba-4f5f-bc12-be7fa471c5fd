/**
 * Player class for blackjack game
 * Handles player/dealer hands, scoring, and game state
 */
class Player {
    constructor(name, isDealer = false) {
        this.name = name;
        this.isDealer = isDealer;
        this.hand = [];
        const startingChips = typeof GameConfig !== 'undefined' ?
            GameConfig.PLAYER.STARTING_CHIPS : 1000;
        this.chips = isDealer ? 0 : startingChips;
        this.currentBet = 0;
        this.isStanding = false;
        this.isBusted = false;
        this.hasBlackjack = false;
        this.handElement = null;
        this.scoreElement = null;
    }

    /**
     * Add a card to the player's hand
     * @param {Card} card - Card to add
     */
    addCard(card) {
        if (card) {
            this.hand.push(card);
            this.updateGameState();
        }
    }

    /**
     * Add multiple cards to the hand
     * @param {Card[]} cards - Cards to add
     */
    addCards(cards) {
        for (let card of cards) {
            this.addCard(card);
        }
    }

    /**
     * Calculate the best possible score for the hand
     * Handles Ace values (1 or 11) optimally
     * @returns {number} Best possible score
     */
    getScore() {
        let score = 0;
        let aces = 0;

        // First pass: count non-ace cards and aces
        for (let card of this.hand) {
            if (card.isHidden) continue; // Skip hidden cards
            
            if (card.isAce()) {
                aces++;
                score += 11; // Start with 11 for aces
            } else {
                score += card.getValue();
            }
        }

        // Adjust aces from 11 to 1 if needed to avoid busting
        while (score > 21 && aces > 0) {
            score -= 10; // Convert ace from 11 to 1
            aces--;
        }

        return score;
    }

    /**
     * Get all possible scores (for display purposes)
     * @returns {number[]} Array of possible scores
     */
    getAllPossibleScores() {
        const scores = [];
        let baseScore = 0;
        let aces = 0;

        for (let card of this.hand) {
            if (card.isHidden) continue;
            
            if (card.isAce()) {
                aces++;
            } else {
                baseScore += card.getValue();
            }
        }

        // Generate all possible scores with different ace values
        for (let highAces = 0; highAces <= aces; highAces++) {
            const lowAces = aces - highAces;
            const score = baseScore + (highAces * 11) + (lowAces * 1);
            if (score <= 21) {
                scores.push(score);
            }
        }

        return [...new Set(scores)].sort((a, b) => b - a);
    }

    /**
     * Check if player has blackjack (21 with 2 cards)
     * @returns {boolean} True if player has blackjack
     */
    checkBlackjack() {
        if (this.hand.length === 2 && this.getScore() === 21) {
            this.hasBlackjack = true;
            return true;
        }
        return false;
    }

    /**
     * Check if player is busted (score > 21)
     * @returns {boolean} True if player is busted
     */
    checkBusted() {
        const score = this.getScore();
        if (score > 21) {
            this.isBusted = true;
            return true;
        }
        return false;
    }

    /**
     * Update game state flags
     */
    updateGameState() {
        this.checkBlackjack();
        this.checkBusted();
    }

    /**
     * Stand (stop taking cards)
     */
    stand() {
        this.isStanding = true;
    }

    /**
     * Place a bet
     * @param {number} amount - Bet amount
     * @returns {boolean} True if bet was successful
     */
    placeBet(amount) {
        if (amount <= this.chips && amount > 0) {
            this.chips -= amount;
            this.currentBet = amount;
            return true;
        }
        return false;
    }

    /**
     * Win chips
     * @param {number} amount - Amount won
     */
    winChips(amount) {
        this.chips += amount;
    }

    /**
     * Clear the current bet
     */
    clearBet() {
        this.currentBet = 0;
    }

    /**
     * Reset hand for new game
     */
    resetHand() {
        this.hand = [];
        this.isStanding = false;
        this.isBusted = false;
        this.hasBlackjack = false;
        this.clearBet();
    }

    /**
     * Get hand summary
     * @returns {Object} Hand information
     */
    getHandSummary() {
        return {
            cards: this.hand.length,
            score: this.getScore(),
            allScores: this.getAllPossibleScores(),
            isBlackjack: this.hasBlackjack,
            isBusted: this.isBusted,
            isStanding: this.isStanding,
            hasHiddenCards: this.hand.some(card => card.isHidden)
        };
    }

    /**
     * Get visible cards (non-hidden)
     * @returns {Card[]} Array of visible cards
     */
    getVisibleCards() {
        return this.hand.filter(card => !card.isHidden);
    }

    /**
     * Get hidden cards
     * @returns {Card[]} Array of hidden cards
     */
    getHiddenCards() {
        return this.hand.filter(card => card.isHidden);
    }

    /**
     * Reveal all hidden cards
     */
    revealAllCards() {
        for (let card of this.hand) {
            if (card.isHidden) {
                card.reveal();
            }
        }
        this.updateGameState();
    }

    /**
     * Can player take another card?
     * @returns {boolean} True if player can hit
     */
    canHit() {
        return !this.isStanding && !this.isBusted && !this.hasBlackjack;
    }

    /**
     * Should dealer hit? (dealer hits on soft 17)
     * @returns {boolean} True if dealer should hit
     */
    shouldDealerHit() {
        if (!this.isDealer) return false;
        
        const score = this.getScore();
        if (score < 17) return true;
        
        // Check for soft 17 (Ace counted as 11) - only if configured to hit soft 17
        if (score === 17) {
            const hitsSoft17 = typeof GameConfig !== 'undefined' ?
                GameConfig.RULES.DEALER_HITS_SOFT_17 : true;

            if (!hitsSoft17) return false;

            let hasAceAs11 = false;
            let tempScore = 0;

            for (let card of this.hand) {
                if (card.isHidden) continue;
                if (card.isAce() && tempScore + 11 <= 17) {
                    hasAceAs11 = true;
                    tempScore += 11;
                } else {
                    tempScore += card.getValue();
                }
            }

            return hasAceAs11;
        }
        
        return false;
    }

    /**
     * Get string representation of hand
     * @returns {string} Hand description
     */
    toString() {
        const visibleCards = this.getVisibleCards();
        const hiddenCount = this.getHiddenCards().length;
        
        let result = `${this.name}: `;
        result += visibleCards.map(card => card.toString()).join(', ');
        
        if (hiddenCount > 0) {
            result += ` + ${hiddenCount} hidden card${hiddenCount > 1 ? 's' : ''}`;
        }
        
        result += ` (Score: ${this.getScore()})`;
        
        return result;
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = Player;
}
