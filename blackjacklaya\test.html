<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BlackJack Game Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #0f4c3a;
            color: white;
            font-family: Arial, sans-serif;
        }
        .game-area {
            max-width: 800px;
            margin: 0 auto;
        }
        .hand {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #fff;
            border-radius: 10px;
        }
        .card {
            display: inline-block;
            width: 60px;
            height: 90px;
            background: white;
            color: black;
            border: 2px solid #000;
            border-radius: 8px;
            margin: 5px;
            text-align: center;
            vertical-align: top;
            font-size: 12px;
            padding: 5px;
        }
        .card.hidden {
            background: #0066cc;
            color: white;
        }
        .buttons {
            text-align: center;
            margin: 20px 0;
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 15px 30px;
            margin: 10px;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
        }
        button:hover {
            background: #45a049;
        }
        button:disabled {
            background: #666;
            cursor: not-allowed;
        }
        .score {
            text-align: center;
            font-size: 24px;
            margin: 20px 0;
        }
        .status {
            text-align: center;
            font-size: 20px;
            margin: 20px 0;
            color: #ffff00;
        }
    </style>
</head>
<body>
    <div class="game-area">
        <h1 style="text-align: center;">BlackJack Game</h1>
        
        <div class="score">
            <span id="scoreDisplay">Player: 0 | Dealer: 0</span>
        </div>
        
        <div class="status">
            <span id="statusDisplay">Welcome to BlackJack! Click Deal to start.</span>
        </div>
        
        <div class="hand">
            <h3>Dealer (<span id="dealerValue">0</span>)</h3>
            <div id="dealerCards"></div>
        </div>
        
        <div class="hand">
            <h3>Player (<span id="playerValue">0</span>)</h3>
            <div id="playerCards"></div>
        </div>
        
        <div class="buttons">
            <button id="dealBtn">Deal Cards</button>
            <button id="hitBtn" disabled>Hit</button>
            <button id="standBtn" disabled>Stand</button>
            <button id="newGameBtn" disabled>New Game</button>
        </div>
    </div>

    <script>
        class Card {
            constructor(suit, rank) {
                this.suit = suit;
                this.rank = rank;
                this.hidden = false;
            }
            
            getValue() {
                if (this.rank === 'A') return 11;
                if (['J', 'Q', 'K'].includes(this.rank)) return 10;
                return parseInt(this.rank);
            }
            
            toString() {
                return this.rank + this.suit;
            }
        }
        
        class Deck {
            constructor() {
                this.cards = [];
                this.createDeck();
                this.shuffle();
            }
            
            createDeck() {
                const suits = ['♥', '♦', '♣', '♠'];
                const ranks = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K'];
                
                for (const suit of suits) {
                    for (const rank of ranks) {
                        this.cards.push(new Card(suit, rank));
                    }
                }
            }
            
            shuffle() {
                for (let i = this.cards.length - 1; i > 0; i--) {
                    const j = Math.floor(Math.random() * (i + 1));
                    [this.cards[i], this.cards[j]] = [this.cards[j], this.cards[i]];
                }
            }
            
            dealCard() {
                return this.cards.pop();
            }
            
            reset() {
                this.cards = [];
                this.createDeck();
                this.shuffle();
            }
        }
        
        class BlackjackGame {
            constructor() {
                this.deck = new Deck();
                this.playerHand = [];
                this.dealerHand = [];
                this.gameState = 'waiting'; // waiting, playing, dealer, finished
                this.playerScore = 0;
                this.dealerScore = 0;
                
                this.initUI();
            }
            
            initUI() {
                document.getElementById('dealBtn').onclick = () => this.dealCards();
                document.getElementById('hitBtn').onclick = () => this.hit();
                document.getElementById('standBtn').onclick = () => this.stand();
                document.getElementById('newGameBtn').onclick = () => this.newGame();
                
                this.updateUI();
            }
            
            dealCards() {
                if (this.gameState !== 'waiting') return;
                
                this.playerHand = [];
                this.dealerHand = [];
                
                if (this.deck.cards.length < 10) {
                    this.deck.reset();
                }
                
                // Deal initial cards
                this.playerHand.push(this.deck.dealCard());
                this.dealerHand.push(this.deck.dealCard());
                this.playerHand.push(this.deck.dealCard());
                
                const dealerSecondCard = this.deck.dealCard();
                dealerSecondCard.hidden = true;
                this.dealerHand.push(dealerSecondCard);
                
                this.gameState = 'playing';
                this.updateUI();
                this.checkForBlackjack();
            }
            
            hit() {
                if (this.gameState !== 'playing') return;
                
                this.playerHand.push(this.deck.dealCard());
                this.updateUI();
                
                if (this.getHandValue(this.playerHand) > 21) {
                    this.endGame('Player Busted! Dealer Wins!');
                    this.dealerScore++;
                }
            }
            
            stand() {
                if (this.gameState !== 'playing') return;
                
                this.gameState = 'dealer';
                this.dealerHand[1].hidden = false;
                this.updateUI();
                this.dealerPlay();
            }
            
            dealerPlay() {
                const dealerValue = this.getHandValue(this.dealerHand);
                
                if (dealerValue < 17) {
                    setTimeout(() => {
                        this.dealerHand.push(this.deck.dealCard());
                        this.updateUI();
                        this.dealerPlay();
                    }, 1000);
                } else {
                    this.determineWinner();
                }
            }
            
            checkForBlackjack() {
                const playerValue = this.getHandValue(this.playerHand);
                if (playerValue === 21 && this.playerHand.length === 2) {
                    this.dealerHand[1].hidden = false;
                    const dealerValue = this.getHandValue(this.dealerHand);
                    
                    if (dealerValue === 21 && this.dealerHand.length === 2) {
                        this.endGame('Both have BlackJack! Push!');
                    } else {
                        this.endGame('BlackJack! Player Wins!');
                        this.playerScore++;
                    }
                }
            }
            
            determineWinner() {
                const playerValue = this.getHandValue(this.playerHand);
                const dealerValue = this.getHandValue(this.dealerHand);
                
                if (dealerValue > 21) {
                    this.endGame('Dealer Busted! Player Wins!');
                    this.playerScore++;
                } else if (playerValue > dealerValue) {
                    this.endGame('Player Wins!');
                    this.playerScore++;
                } else if (dealerValue > playerValue) {
                    this.endGame('Dealer Wins!');
                    this.dealerScore++;
                } else {
                    this.endGame('Push!');
                }
            }
            
            endGame(message) {
                this.gameState = 'finished';
                document.getElementById('statusDisplay').textContent = message;
                this.updateUI();
            }
            
            newGame() {
                this.gameState = 'waiting';
                this.playerHand = [];
                this.dealerHand = [];
                this.updateUI();
            }
            
            getHandValue(hand) {
                let value = 0;
                let aces = 0;
                
                for (const card of hand) {
                    if (card.rank === 'A') {
                        aces++;
                        value += 11;
                    } else {
                        value += card.getValue();
                    }
                }
                
                while (value > 21 && aces > 0) {
                    value -= 10;
                    aces--;
                }
                
                return value;
            }
            
            updateUI() {
                // Update card displays
                this.displayHand('dealerCards', this.dealerHand);
                this.displayHand('playerCards', this.playerHand);
                
                // Update values
                const playerValue = this.getHandValue(this.playerHand);
                const dealerValue = this.gameState === 'playing' ? 
                    this.getHandValue([this.dealerHand[0]]) : 
                    this.getHandValue(this.dealerHand);
                
                document.getElementById('playerValue').textContent = playerValue;
                document.getElementById('dealerValue').textContent = dealerValue;
                document.getElementById('scoreDisplay').textContent = 
                    `Player: ${this.playerScore} | Dealer: ${this.dealerScore}`;
                
                // Update buttons
                document.getElementById('dealBtn').disabled = this.gameState !== 'waiting';
                document.getElementById('hitBtn').disabled = this.gameState !== 'playing';
                document.getElementById('standBtn').disabled = this.gameState !== 'playing';
                document.getElementById('newGameBtn').disabled = this.gameState !== 'finished';
                
                // Update status
                if (this.gameState === 'waiting') {
                    document.getElementById('statusDisplay').textContent = 'Click Deal to start a new hand';
                }
            }
            
            displayHand(containerId, hand) {
                const container = document.getElementById(containerId);
                container.innerHTML = '';
                
                for (const card of hand) {
                    const cardDiv = document.createElement('div');
                    cardDiv.className = 'card' + (card.hidden ? ' hidden' : '');
                    cardDiv.textContent = card.hidden ? 'CARD' : card.toString();
                    container.appendChild(cardDiv);
                }
            }
        }
        
        // Start the game
        window.addEventListener('load', () => {
            new BlackjackGame();
        });
    </script>
</body>
</html>
