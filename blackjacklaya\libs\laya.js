window.Laya = {
    init: function(width, height) {
        return new Promise((resolve) => {
            const canvas = document.createElement('canvas');
            canvas.width = width;
            canvas.height = height;
            canvas.style.width = '100%';
            canvas.style.height = '100%';
            canvas.style.display = 'block';

            document.getElementById('layaContainer').appendChild(canvas);
            document.getElementById('loading').style.display = 'none';

            this.stage = new Stage(canvas);
            this.canvas = canvas;
            this.context = canvas.getContext('2d');

            this.setupGlobalClickHandler();

            setTimeout(resolve, 100);
        });
    },

    setupGlobalClickHandler: function() {
        this.canvas.addEventListener('click', (e) => {
            const rect = this.canvas.getBoundingClientRect();
            const rawX = e.clientX - rect.left;
            const rawY = e.clientY - rect.top;

            // 调整坐标以匹配画布的实际尺寸
            const scaleX = this.canvas.width / rect.width;
            const scaleY = this.canvas.height / rect.height;
            const x = rawX * scaleX;
            const y = rawY * scaleY;

            console.log(`Global click at: raw(${rawX}, ${rawY}) scaled(${x}, ${y}) canvas(${this.canvas.width}x${this.canvas.height}) display(${rect.width}x${rect.height})`);
            this.handleClick(this.stage, x, y);
        });
    },

    handleClick: function(container, x, y) {
        console.log(`Checking container with ${container.children.length} children`);

        for (const child of container.children) {
            if (child instanceof Button && child.visible !== false) {
                console.log(`Checking button: ${child.label} at (${child.x}, ${child.y}) size (${child.width}, ${child.height})`);
                if (child.isPointInside(x, y)) {
                    console.log('Button clicked:', child.label);
                    child.emit('click');
                    return true;
                }
            }

            if (child.children && child.children.length > 0) {
                const childX = x - (child.x || 0);
                const childY = y - (child.y || 0);
                console.log(`Checking child container at (${child.x}, ${child.y}), relative click (${childX}, ${childY})`);
                if (this.handleClick(child, childX, childY)) {
                    return true;
                }
            }
        }
        return false;
    },

    stage: null,
    canvas: null,
    context: null
};

class Stage {
    constructor(canvas) {
        this.canvas = canvas;
        this.context = canvas.getContext('2d');
        this.children = [];
        this.scaleMode = 'fixedWidth';
        this.screenMode = 'horizontal';
        this.alignV = 'middle';
        this.alignH = 'center';
        
        this.startRenderLoop();
    }
    
    addChild(child) {
        this.children.push(child);
        child.parent = this;
    }
    
    removeChild(child) {
        const index = this.children.indexOf(child);
        if (index > -1) {
            this.children.splice(index, 1);
            child.parent = null;
        }
    }
    
    startRenderLoop() {
        const render = () => {
            this.context.clearRect(0, 0, this.canvas.width, this.canvas.height);
            this.renderChildren();
            requestAnimationFrame(render);
        };
        render();
    }
    
    renderChildren() {
        for (const child of this.children) {
            if (child.visible !== false) {
                child.render(this.context);
            }
        }
    }
}

Stage.SCALE_FIXED_WIDTH = 'fixedWidth';
Stage.SCREEN_HORIZONTAL = 'horizontal';
Stage.ALIGN_MIDDLE = 'middle';
Stage.ALIGN_CENTER = 'center';

class Sprite {
    constructor() {
        this.x = 0;
        this.y = 0;
        this.width = 0;
        this.height = 0;
        this.visible = true;
        this.children = [];
        this.parent = null;
        this.graphics = new Graphics();
        this.events = {};
    }
    
    addChild(child) {
        this.children.push(child);
        child.parent = this;
    }
    
    removeChild(child) {
        const index = this.children.indexOf(child);
        if (index > -1) {
            this.children.splice(index, 1);
            child.parent = null;
        }
    }
    
    on(event, context, handler) {
        if (!this.events[event]) {
            this.events[event] = [];
        }
        this.events[event].push({ context, handler });
    }
    
    emit(event, ...args) {
        if (this.events[event]) {
            for (const listener of this.events[event]) {
                listener.handler.call(listener.context, ...args);
            }
        }
    }
    
    render(ctx) {
        ctx.save();
        ctx.translate(this.x, this.y);
        
        this.graphics.render(ctx);
        
        for (const child of this.children) {
            if (child.visible !== false) {
                child.render(ctx);
            }
        }
        
        ctx.restore();
    }
}

class Graphics {
    constructor() {
        this.commands = [];
    }
    
    drawRect(x, y, width, height, fillColor, strokeColor, strokeWidth) {
        this.commands.push({
            type: 'rect',
            x, y, width, height, fillColor, strokeColor, strokeWidth
        });
    }
    
    render(ctx) {
        for (const cmd of this.commands) {
            if (cmd.type === 'rect') {
                if (cmd.fillColor) {
                    ctx.fillStyle = cmd.fillColor;
                    ctx.fillRect(cmd.x, cmd.y, cmd.width, cmd.height);
                }
                if (cmd.strokeColor && cmd.strokeWidth) {
                    ctx.strokeStyle = cmd.strokeColor;
                    ctx.lineWidth = cmd.strokeWidth;
                    ctx.strokeRect(cmd.x, cmd.y, cmd.width, cmd.height);
                }
            }
        }
    }
}

class Text extends Sprite {
    constructor() {
        super();
        this.text = '';
        this.fontSize = 16;
        this.color = '#000000';
        this.fontFamily = 'Arial';
    }
    
    render(ctx) {
        ctx.save();
        ctx.translate(this.x, this.y);
        ctx.fillStyle = this.color;
        ctx.font = `${this.fontSize}px ${this.fontFamily}`;
        ctx.fillText(this.text, 0, this.fontSize);
        ctx.restore();
    }
}

class Button extends Sprite {
    constructor() {
        super();
        this.label = '';
        this.width = 100;
        this.height = 30;
        this.skin = null;
    }
    
    isPointInside(x, y) {
        const globalPos = this.getGlobalPosition();
        const result = x >= globalPos.x && x <= globalPos.x + this.width &&
                      y >= globalPos.y && y <= globalPos.y + this.height;

        if (result) {
            console.log(`Button ${this.label} hit test: point(${x},${y}) vs button(${globalPos.x},${globalPos.y},${this.width},${this.height})`);
        }

        return result;
    }

    getGlobalPosition() {
        let x = this.x;
        let y = this.y;
        let parent = this.parent;

        while (parent && parent !== Laya.stage) {
            x += parent.x || 0;
            y += parent.y || 0;
            parent = parent.parent;
        }

        return { x, y };
    }
    
    render(ctx) {
        ctx.save();
        ctx.translate(this.x, this.y);
        
        ctx.fillStyle = '#4CAF50';
        ctx.fillRect(0, 0, this.width, this.height);
        ctx.strokeStyle = '#45a049';
        ctx.lineWidth = 2;
        ctx.strokeRect(0, 0, this.width, this.height);
        
        ctx.fillStyle = '#ffffff';
        ctx.font = '14px Arial';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(this.label, this.width / 2, this.height / 2);
        
        ctx.restore();
    }
}

class Stat {
    static show() {
        console.log('Stats enabled');
    }
}

class Tween {
    static to(target, props, duration, ease) {
        const startProps = {};
        for (const prop in props) {
            startProps[prop] = target[prop];
        }
        
        const startTime = Date.now();
        
        const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            for (const prop in props) {
                const start = startProps[prop];
                const end = props[prop];
                target[prop] = start + (end - start) * progress;
            }
            
            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };
        
        animate();
    }
}

class Ease {
    static backOut(t) {
        const c1 = 1.70158;
        const c3 = c1 + 1;
        return 1 + c3 * Math.pow(t - 1, 3) + c1 * Math.pow(t - 1, 2);
    }
}

window.Sprite = Sprite;
window.Text = Text;
window.Button = Button;
window.Graphics = Graphics;
window.Stat = Stat;
window.Tween = Tween;
window.Ease = Ease;
